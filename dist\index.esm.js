// src/core.ts
import { BroadcastChannel as BroadcastChannelImpl } from "broadcast-channel";

// src/utils.ts
import superjson from "superjson";
function cloneDeepSerializable(obj, serializer = {
  serialize: superjson.stringify,
  deserialize: superjson.parse
}) {
  return serializer.deserialize(serializer.serialize(obj));
}
function getProxyId(proxy) {
  if (!proxy._valtioSharedId) {
    proxy._valtioSharedId = `valtio-${Math.random().toString(36).substr(2, 9)}`;
  }
  return proxy._valtioSharedId;
}

// src/core.ts
import { subscribe, snapshot } from "valtio/vanilla";
import { noop, set } from "lodash";
function shareValtioState(proxy, { enable = true, initialize = true } = {}) {
  if (!enable) {
    return noop;
  }
  const channel = new BroadcastChannelImpl(getProxyId(proxy), {
    type: "native"
  });
  let timestamp = 0;
  let externalUpdate = false;
  const keysToUpdate = Object.keys(snapshot(proxy));
  channel.onmessage = (newState) => {
    if (newState === void 0) {
      channel.postMessage({
        timestamp,
        state: cloneDeepSerializable(snapshot(proxy))
      });
      return;
    }
    if (newState.timestamp <= timestamp) {
      return;
    }
    externalUpdate = true;
    timestamp = newState.timestamp;
    keysToUpdate.forEach((key) => {
      if (key in newState.state) {
        set(proxy, key, newState.state[key]);
      }
    });
  };
  const unsubscribe = subscribe(proxy, () => {
    if (!externalUpdate) {
      timestamp = Date.now();
      channel.postMessage({
        timestamp,
        state: cloneDeepSerializable(snapshot(proxy))
      });
    }
    externalUpdate = false;
  });
  if (initialize) {
    channel.postMessage(void 0);
  }
  return () => {
    unsubscribe();
    channel.close();
  };
}

// src/hook.ts
import { useRef, useEffect } from "react";
function useSharedValtioState(proxy, options = {}) {
  const unshareRef = useRef(null);
  useEffect(() => {
    unshareRef.current = shareValtioState(proxy, options);
    return () => {
      if (unshareRef.current) {
        unshareRef.current();
        unshareRef.current = null;
      }
    };
  }, [proxy, options]);
}
export {
  cloneDeepSerializable,
  getProxyId,
  shareValtioState,
  useSharedValtioState
};
