"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var index_exports = {};
__export(index_exports, {
  cloneDeepSerializable: () => cloneDeepSerializable,
  getProxyId: () => getProxyId,
  shareValtioState: () => shareValtioState,
  useSharedValtioState: () => useSharedValtioState
});
module.exports = __toCommonJS(index_exports);

// src/core.ts
var import_broadcast_channel = require("broadcast-channel");

// src/utils.ts
var import_superjson = __toESM(require("superjson"));
function cloneDeepSerializable(obj, serializer = {
  serialize: import_superjson.default.stringify,
  deserialize: import_superjson.default.parse
}) {
  return serializer.deserialize(serializer.serialize(obj));
}
function getProxyId(proxy) {
  if (!proxy._valtioSharedId) {
    proxy._valtioSharedId = `valtio-${Math.random().toString(36).substr(2, 9)}`;
  }
  return proxy._valtioSharedId;
}

// src/core.ts
var import_vanilla = require("valtio/vanilla");
var import_lodash = require("lodash");
function shareValtioState(proxy, { enable = true, initialize = true } = {}) {
  if (!enable) {
    return import_lodash.noop;
  }
  const channel = new import_broadcast_channel.BroadcastChannel(getProxyId(proxy), {
    type: "native"
  });
  let timestamp = 0;
  let externalUpdate = false;
  const keysToUpdate = Object.keys((0, import_vanilla.snapshot)(proxy));
  channel.onmessage = (newState) => {
    if (newState === void 0) {
      channel.postMessage({
        timestamp,
        state: cloneDeepSerializable((0, import_vanilla.snapshot)(proxy))
      });
      return;
    }
    if (newState.timestamp <= timestamp) {
      return;
    }
    externalUpdate = true;
    timestamp = newState.timestamp;
    keysToUpdate.forEach((key) => {
      if (key in newState.state) {
        (0, import_lodash.set)(proxy, key, newState.state[key]);
      }
    });
  };
  const unsubscribe = (0, import_vanilla.subscribe)(proxy, () => {
    if (!externalUpdate) {
      timestamp = Date.now();
      channel.postMessage({
        timestamp,
        state: cloneDeepSerializable((0, import_vanilla.snapshot)(proxy))
      });
    }
    externalUpdate = false;
  });
  if (initialize) {
    channel.postMessage(void 0);
  }
  return () => {
    unsubscribe();
    channel.close();
  };
}

// src/hook.ts
var import_react = require("react");
function useSharedValtioState(proxy, options = {}) {
  const unshareRef = (0, import_react.useRef)(null);
  (0, import_react.useEffect)(() => {
    unshareRef.current = shareValtioState(proxy, options);
    return () => {
      if (unshareRef.current) {
        unshareRef.current();
        unshareRef.current = null;
      }
    };
  }, [proxy, options]);
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  cloneDeepSerializable,
  getProxyId,
  shareValtioState,
  useSharedValtioState
});
