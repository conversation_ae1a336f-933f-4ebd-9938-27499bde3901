hoistPattern:
  - '*'
hoistedDependencies:
  '@babel/runtime@7.27.0':
    '@babel/runtime': private
  '@esbuild/aix-ppc64@0.25.8':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.8':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.8':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.8':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.8':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.8':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.8':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.8':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.8':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.8':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.8':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.8':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.8':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.8':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.8':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.8':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.8':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.8':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.8':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.8':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.8':
    '@esbuild/openbsd-x64': private
  '@esbuild/openharmony-arm64@0.25.8':
    '@esbuild/openharmony-arm64': private
  '@esbuild/sunos-x64@0.25.8':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.8':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.8':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.8':
    '@esbuild/win32-x64': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@rollup/rollup-android-arm-eabi@4.45.1':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.45.1':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.45.1':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.45.1':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.45.1':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.45.1':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.45.1':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.45.1':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.45.1':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.45.1':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.45.1':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.45.1':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.45.1':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.45.1':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.45.1':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.45.1':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.45.1':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.45.1':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.45.1':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.45.1':
    '@rollup/rollup-win32-x64-msvc': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/prop-types@15.7.15':
    '@types/prop-types': private
  acorn@8.15.0:
    acorn: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  balanced-match@1.0.2:
    balanced-match: private
  brace-expansion@2.0.2:
    brace-expansion: private
  bundle-require@5.1.0(esbuild@0.25.8):
    bundle-require: private
  cac@6.7.14:
    cac: private
  chokidar@4.0.3:
    chokidar: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  commander@4.1.1:
    commander: private
  confbox@0.1.8:
    confbox: private
  consola@3.4.2:
    consola: private
  copy-anything@3.0.5:
    copy-anything: private
  cross-spawn@7.0.6:
    cross-spawn: private
  csstype@3.1.3:
    csstype: private
  debug@4.4.1:
    debug: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  emoji-regex@8.0.0:
    emoji-regex: private
  esbuild@0.25.8:
    esbuild: private
  eventemitter3@4.0.7:
    eventemitter3: private
  fdir@6.4.6(picomatch@4.0.3):
    fdir: private
  fix-dts-default-cjs-exports@1.0.1:
    fix-dts-default-cjs-exports: private
  foreground-child@3.3.1:
    foreground-child: private
  fsevents@2.3.3:
    fsevents: private
  glob@10.4.5:
    glob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-what@4.1.16:
    is-what: private
  isexe@2.0.0:
    isexe: private
  jackspeak@3.4.3:
    jackspeak: private
  joycon@3.1.1:
    joycon: private
  js-tokens@4.0.0:
    js-tokens: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  load-tsconfig@0.2.5:
    load-tsconfig: private
  lodash.sortby@4.7.0:
    lodash.sortby: private
  loose-envify@1.4.0:
    loose-envify: private
  lru-cache@10.4.3:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  minimatch@9.0.5:
    minimatch: private
  minipass@7.1.2:
    minipass: private
  mlly@1.7.4:
    mlly: private
  ms@2.1.3:
    ms: private
  mz@2.7.0:
    mz: private
  object-assign@4.1.1:
    object-assign: private
  oblivious-set@1.4.0:
    oblivious-set: private
  p-finally@1.0.0:
    p-finally: private
  p-queue@6.6.2:
    p-queue: private
  p-timeout@3.2.0:
    p-timeout: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  path-key@3.1.1:
    path-key: private
  path-scurry@1.11.1:
    path-scurry: private
  pathe@2.0.3:
    pathe: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  pirates@4.0.7:
    pirates: private
  pkg-types@1.3.1:
    pkg-types: private
  postcss-load-config@6.0.1:
    postcss-load-config: private
  proxy-compare@3.0.1:
    proxy-compare: private
  punycode@2.3.1:
    punycode: private
  readdirp@4.1.2:
    readdirp: private
  regenerator-runtime@0.14.1:
    regenerator-runtime: private
  resolve-from@5.0.0:
    resolve-from: private
  rollup@4.45.1:
    rollup: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  signal-exit@4.1.0:
    signal-exit: private
  source-map@0.8.0-beta.0:
    source-map: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@5.1.2:
    string-width: private
  strip-ansi@6.0.1:
    strip-ansi-cjs: private
  strip-ansi@7.1.0:
    strip-ansi: private
  sucrase@3.35.0:
    sucrase: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  tinyexec@0.3.2:
    tinyexec: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tr46@1.0.1:
    tr46: private
  tree-kill@1.2.2:
    tree-kill: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  ufo@1.6.1:
    ufo: private
  unload@2.4.1:
    unload: private
  webidl-conversions@4.0.2:
    webidl-conversions: private
  whatwg-url@7.1.0:
    whatwg-url: private
  which@2.0.2:
    which: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@8.1.0:
    wrap-ansi: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.12.1
pendingBuilds: []
prunedAt: Sun, 20 Jul 2025 07:26:53 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmmirror.com/
skipped:
  - '@esbuild/aix-ppc64@0.25.8'
  - '@esbuild/android-arm64@0.25.8'
  - '@esbuild/android-arm@0.25.8'
  - '@esbuild/android-x64@0.25.8'
  - '@esbuild/darwin-arm64@0.25.8'
  - '@esbuild/darwin-x64@0.25.8'
  - '@esbuild/freebsd-arm64@0.25.8'
  - '@esbuild/freebsd-x64@0.25.8'
  - '@esbuild/linux-arm64@0.25.8'
  - '@esbuild/linux-arm@0.25.8'
  - '@esbuild/linux-ia32@0.25.8'
  - '@esbuild/linux-loong64@0.25.8'
  - '@esbuild/linux-mips64el@0.25.8'
  - '@esbuild/linux-ppc64@0.25.8'
  - '@esbuild/linux-riscv64@0.25.8'
  - '@esbuild/linux-s390x@0.25.8'
  - '@esbuild/linux-x64@0.25.8'
  - '@esbuild/netbsd-arm64@0.25.8'
  - '@esbuild/netbsd-x64@0.25.8'
  - '@esbuild/openbsd-arm64@0.25.8'
  - '@esbuild/openbsd-x64@0.25.8'
  - '@esbuild/openharmony-arm64@0.25.8'
  - '@esbuild/sunos-x64@0.25.8'
  - '@esbuild/win32-arm64@0.25.8'
  - '@esbuild/win32-ia32@0.25.8'
  - '@rollup/rollup-android-arm-eabi@4.45.1'
  - '@rollup/rollup-android-arm64@4.45.1'
  - '@rollup/rollup-darwin-arm64@4.45.1'
  - '@rollup/rollup-darwin-x64@4.45.1'
  - '@rollup/rollup-freebsd-arm64@4.45.1'
  - '@rollup/rollup-freebsd-x64@4.45.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.45.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.45.1'
  - '@rollup/rollup-linux-arm64-gnu@4.45.1'
  - '@rollup/rollup-linux-arm64-musl@4.45.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.45.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.45.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.45.1'
  - '@rollup/rollup-linux-riscv64-musl@4.45.1'
  - '@rollup/rollup-linux-s390x-gnu@4.45.1'
  - '@rollup/rollup-linux-x64-gnu@4.45.1'
  - '@rollup/rollup-linux-x64-musl@4.45.1'
  - '@rollup/rollup-win32-arm64-msvc@4.45.1'
  - '@rollup/rollup-win32-ia32-msvc@4.45.1'
  - fsevents@2.3.3
storeDir: D:\.pnpm-store\v3
virtualStoreDir: D:\Projects\valtab\node_modules\.pnpm
virtualStoreDirMaxLength: 120
