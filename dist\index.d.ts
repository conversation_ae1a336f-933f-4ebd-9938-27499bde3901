interface ShareOptions {
    enable?: boolean;
    initialize?: boolean;
}
/**
 * Share a Valtio proxy state across browser tabs.
 *
 * @example
 *
 * ```ts
 * import { proxy } from 'valtio'
 * import { shareValtioState } from 'valtio-shared-state'
 * const state = proxy({ count: 0, text: 'hello' })
 * const unshare = shareValtioState(state, {})
 * ```
 *
 * @param proxy - The Valtio proxy object to share
 * @param options - Share options
 * @param options.enable - Enable/disable sharing of state
 * @param options.initialize - Immediately recover the shared state from another tab
 */
declare function shareValtioState(proxy: object, { enable, initialize }?: ShareOptions): () => void;

/**
 * React hook to share a Valtio proxy state across browser tabs.
 * Automatically handles cleanup when the component unmounts.
 *
 * @example
 *
 * ```tsx
 * import { proxy, useSnapshot } from 'valtio'
 * import { useSharedValtioState } from 'valtio-shared-state'

 * function MyComponent() {
 *   useSharedValtioState(state, {})
 *   const snap = useSnapshot(state)
 *
 *   return (
 *     <div>
 *       <p>Count: {snap.count}</p>
 *       <button onClick={() => state.count++}>Increment</button>
 *     </div>
 *   )
 * }
 * ```
 *
 * @param proxy - The Valtio proxy object to share
 * @param options - Share options (same as shareValtioState)
 */
declare function useSharedValtioState(proxy: object, options?: ShareOptions): void;

interface Serializer {
    serialize: (value: any) => string;
    deserialize: (value: string) => any;
}
declare function cloneDeepSerializable(obj: object, serializer?: Serializer): any;
/**
 * Generate a unique identifier for a proxy object
 * This is used as the channel name for BroadcastChannel
 */
declare function getProxyId(proxy: any): string;

export { type Serializer, type ShareOptions, cloneDeepSerializable, getProxyId, shareValtioState, useSharedValtioState };
